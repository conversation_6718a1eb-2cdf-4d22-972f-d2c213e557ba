"use server";

import { revalidatePath } from "next/cache";
import { updateAllTrendingData, forceUpdateTrendingData } from "../firebase/trending/actions";
import { shouldUpdateTrendingData, getTrendingDataLastUpdate } from "../firebase/trending/service";

/**
 * Main trending aggregation function
 * This function checks if trending data needs to be updated and performs the update if necessary
 * @param force Whether to force update regardless of timing (default: false)
 * @returns Promise that resolves with update status
 */
export async function aggregateTrendingData(force: boolean = false): Promise<{
   updated: boolean;
   lastUpdate: Date | null;
   nextUpdate: Date | null;
   message: string;
}> {
   try {
      console.log(`Starting trending data aggregation (force: ${force})...`);

      // Check if update is needed (unless forced)
      const needsUpdate = force || await shouldUpdateTrendingData("overall");
      
      if (!needsUpdate) {
         const lastUpdate = await getTrendingDataLastUpdate("overall");
         console.log("Trending data is up to date, skipping update");
         
         return {
            updated: false,
            lastUpdate,
            nextUpdate: lastUpdate ? new Date(lastUpdate.getTime() + 8 * 60 * 60 * 1000) : null,
            message: "Trending data is up to date"
         };
      }

      // Perform the update
      if (force) {
         await forceUpdateTrendingData();
      } else {
         await updateAllTrendingData();
      }

      // Get the updated timestamp
      const lastUpdate = await getTrendingDataLastUpdate("overall");
      const nextUpdate = lastUpdate ? new Date(lastUpdate.getTime() + 8 * 60 * 60 * 1000) : null;

      // Revalidate relevant pages
      revalidatePath("/");
      revalidatePath("/trending");
      revalidatePath("/categories");

      console.log("Trending data aggregation completed successfully");

      return {
         updated: true,
         lastUpdate,
         nextUpdate,
         message: "Trending data updated successfully"
      };
   } catch (error) {
      console.error("Error during trending data aggregation:", error);
      throw new Error(`Failed to aggregate trending data: ${error instanceof Error ? error.message : 'Unknown error'}`);
   }
}

/**
 * Scheduled trending data update function
 * This function is designed to be called by a cron job or scheduled task
 * @returns Promise that resolves with update status
 */
export async function scheduledTrendingUpdate(): Promise<{
   success: boolean;
   message: string;
   timestamp: Date;
}> {
   const timestamp = new Date();
   
   try {
      console.log(`Scheduled trending update started at ${timestamp.toISOString()}`);
      
      const result = await aggregateTrendingData(false);
      
      return {
         success: true,
         message: result.updated ? "Trending data updated" : "No update needed",
         timestamp
      };
   } catch (error) {
      console.error("Scheduled trending update failed:", error);
      
      return {
         success: false,
         message: `Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
         timestamp
      };
   }
}

/**
 * Manual trending data refresh function
 * This function forces an immediate update of all trending data
 * @returns Promise that resolves with update status
 */
export async function manualTrendingRefresh(): Promise<{
   success: boolean;
   message: string;
   timestamp: Date;
   lastUpdate: Date | null;
   nextUpdate: Date | null;
}> {
   const timestamp = new Date();
   
   try {
      console.log(`Manual trending refresh started at ${timestamp.toISOString()}`);
      
      const result = await aggregateTrendingData(true);
      
      return {
         success: true,
         message: "Trending data force updated successfully",
         timestamp,
         lastUpdate: result.lastUpdate,
         nextUpdate: result.nextUpdate
      };
   } catch (error) {
      console.error("Manual trending refresh failed:", error);
      
      return {
         success: false,
         message: `Refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
         timestamp,
         lastUpdate: null,
         nextUpdate: null
      };
   }
}

/**
 * Get trending data status
 * Returns information about the current state of trending data
 * @returns Promise that resolves with status information
 */
export async function getTrendingDataStatus(): Promise<{
   lastUpdate: Date | null;
   nextUpdate: Date | null;
   needsUpdate: boolean;
   hoursUntilNextUpdate: number | null;
}> {
   try {
      const lastUpdate = await getTrendingDataLastUpdate("overall");
      const needsUpdate = await shouldUpdateTrendingData("overall");
      
      let nextUpdate: Date | null = null;
      let hoursUntilNextUpdate: number | null = null;
      
      if (lastUpdate) {
         nextUpdate = new Date(lastUpdate.getTime() + 8 * 60 * 60 * 1000);
         const now = new Date();
         const msUntilUpdate = nextUpdate.getTime() - now.getTime();
         hoursUntilNextUpdate = Math.max(0, msUntilUpdate / (1000 * 60 * 60));
      }
      
      return {
         lastUpdate,
         nextUpdate,
         needsUpdate,
         hoursUntilNextUpdate
      };
   } catch (error) {
      console.error("Error getting trending data status:", error);
      
      return {
         lastUpdate: null,
         nextUpdate: null,
         needsUpdate: true,
         hoursUntilNextUpdate: null
      };
   }
}
